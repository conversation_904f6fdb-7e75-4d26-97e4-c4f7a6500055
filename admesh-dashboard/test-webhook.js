#!/usr/bin/env node

/**
 * Test script for Sanity webhook revalidation
 * Usage: node test-webhook.js [environment]
 * Environment: development (default) | production
 */

const crypto = require('crypto');

const environment = process.argv[2] || 'development';

const config = {
  development: {
    url: 'http://localhost:3001/api/revalidate',
    secret: 'dev_secret_for_sanity_webhook_123'
  },
  production: {
    url: 'https://useadmesh.com/api/revalidate',
    secret: 'your_random_secret_string_here' // Update this with your actual production secret
  }
};

const { url, secret } = config[environment];

if (!secret || secret === 'your_random_secret_string_here') {
  console.error('❌ Please update the secret in the config for', environment);
  process.exit(1);
}

// Test payload simulating a blog post update
const testPayload = {
  _type: 'post',
  slug: {
    current: 'test-blog-post'
  }
};

const body = JSON.stringify(testPayload);

// Generate signature (same as <PERSON><PERSON> does)
const signature = crypto
  .createHmac('sha256', secret)
  .update(body)
  .digest('hex');

console.log(`🧪 Testing webhook revalidation for ${environment}...`);
console.log(`📡 URL: ${url}`);
console.log(`📝 Payload:`, testPayload);
console.log(`🔐 Signature: sha256=${signature}`);

fetch(url, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'sanity-webhook-signature': `sha256=${signature}`
  },
  body: body
})
.then(response => {
  console.log(`📊 Status: ${response.status} ${response.statusText}`);
  return response.json();
})
.then(data => {
  console.log('✅ Response:', data);
  if (data.message === 'Revalidation successful') {
    console.log('🎉 Webhook test passed!');
  } else {
    console.log('⚠️  Unexpected response');
  }
})
.catch(error => {
  console.error('❌ Webhook test failed:', error.message);
  
  if (error.code === 'ECONNREFUSED') {
    console.log('💡 Make sure your development server is running: npm run dev');
  }
});
