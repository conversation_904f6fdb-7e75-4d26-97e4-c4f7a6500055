# Sanity Webhook Setup for Blog Revalidation

This guide explains how to set up Sanity webhooks to automatically revalidate your Next.js blog pages when content is updated in Sanity Studio.

## Problem Solved

When you add new blog posts in Sanity Studio, they appear in localhost (development) but not in production because:
1. Production uses cached/static pages for better performance
2. There's no mechanism to tell Next.js when Sanity content changes
3. Pages need to be revalidated when new content is published

## Solution Overview

We've implemented:
1. **ISR (Incremental Static Regeneration)** with time-based revalidation (60 seconds)
2. **Tag-based revalidation** for instant updates via webhooks
3. **Webhook API route** (`/api/revalidate`) to handle Sanity content changes
4. **Static generation** for blog post pages

## Setup Instructions

### 1. Environment Variables

The following environment variables have been added to your `.env` files:

```bash
# Development
SANITY_REVALIDATE_SECRET=dev_secret_for_sanity_webhook_123

# Production (update with a secure random string)
SANITY_REVALIDATE_SECRET=your_random_secret_string_here
```

**Important**: For production, generate a secure random string:
```bash
# Generate a secure secret
openssl rand -base64 32
```

### 2. Sanity Webhook Configuration

1. Go to your Sanity project dashboard: https://www.sanity.io/manage
2. Navigate to your project (`h0kukqbi`)
3. Go to **API** → **Webhooks**
4. Click **Create webhook**

Configure the webhook with:

**Name**: `AdMesh Blog Revalidation`

**URL**: 
- Development: `http://localhost:3000/api/revalidate`
- Production: `https://useadmesh.com/api/revalidate`

**Dataset**: `production`

**Trigger on**: 
- Create
- Update  
- Delete

**Filter**: 
```groq
_type == "post" || _type == "author" || _type == "category"
```

**Projection**:
```groq
{
  _type,
  "slug": slug.current
}
```

**Secret**: Use the same value as `SANITY_REVALIDATE_SECRET`

**HTTP method**: `POST`

### 3. Testing the Setup

#### Test in Development:
1. Start your development server: `npm run dev`
2. Add a new blog post in Sanity Studio
3. Check the webhook logs in Sanity dashboard
4. Verify the post appears immediately on `http://localhost:3000/blog`

#### Test in Production:
1. Deploy your changes to production
2. Update the production environment variable with your secure secret
3. Add the production webhook URL in Sanity
4. Add a new blog post in Sanity Studio
5. Check that it appears on `https://useadmesh.com/blog` within 60 seconds

## How It Works

### 1. Time-based Revalidation
- Blog pages revalidate every 60 seconds automatically
- Ensures content is never more than 1 minute stale

### 2. Webhook-based Revalidation
- Instant revalidation when content changes in Sanity
- Revalidates specific pages and cache tags
- Updates sitemap and RSS feed

### 3. Static Generation
- Blog post pages are pre-generated at build time
- New posts are generated on-demand
- Improves performance and SEO

## Troubleshooting

### Webhook Not Working
1. Check webhook logs in Sanity dashboard
2. Verify the secret matches in both places
3. Check Next.js server logs for errors
4. Ensure the webhook URL is accessible

### Content Still Not Updating
1. Wait up to 60 seconds for time-based revalidation
2. Check if the webhook is triggering correctly
3. Verify the content is published (not draft) in Sanity
4. Clear browser cache and try again

### Development Issues
1. Ensure your dev server is running on port 3000
2. Use ngrok for testing webhooks locally:
   ```bash
   npx ngrok http 3000
   # Use the ngrok URL in Sanity webhook config
   ```

## Files Modified

- `src/app/blog/page.tsx` - Added ISR and cache tags
- `src/app/blog/[slug]/page.tsx` - Added ISR, cache tags, and static params
- `src/app/blog/sitemap.xml/route.ts` - Added cache tags
- `src/app/blog/rss.xml/route.ts` - Added cache tags
- `src/app/api/revalidate/route.ts` - New webhook handler
- `.env.*` - Added revalidation secret

## Next Steps

1. Generate and set a secure `SANITY_REVALIDATE_SECRET` for production
2. Create the webhook in your Sanity project dashboard
3. Deploy the changes to production
4. Test the complete flow with a new blog post

The blog should now update automatically in production when you add new content in Sanity Studio!
