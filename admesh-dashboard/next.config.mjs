/** @type {import('next').NextConfig} */
const nextConfig = {
  // output: "export", // ✅ enables static export

  // Skip ESLint during build
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Skip TypeScript checking during build
  typescript: {
    ignoreBuildErrors: true,
  },

  // Enable fetch logging for debugging caching and revalidation
  logging: {
    fetches: {
      fullUrl: true,
    },
  },

  // Improve SEO by adding trailing slashes to URLs
  trailingSlash: true,

  // Optimize images
  images: {
    formats: ['image/avif', 'image/webp'],
    domains: ['firebasestorage.googleapis.com', 'cdn.sanity.io'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'firebasestorage.googleapis.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'cdn.sanity.io',
        port: '',
        pathname: '/**',
      },
    ],
    minimumCacheTTL: 60,
  },

  // Headers for SEO and security
  async headers() {
    return [
      // Default headers for all pages
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'X-Robots-Tag',
            value: 'index, follow, max-image-preview:large, max-snippet:-1',
          },
        ],
      },
      // No-index headers for dashboard and private pages
      {
        source: '/dashboard/:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow',
          },
        ],
      },
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow',
          },
        ],
      },
      // No-index headers for test pages
      {
        source: '/test-:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow',
          },
        ],
      },
      {
        source: '/seo-test',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow',
          },
        ],
      },
    ];
  },

  // Redirects for SEO - Standardize to non-www canonical domain
  async redirects() {
    return [
      // Redirect www to non-www for canonical domain consistency
      {
        source: '/:path*',
        has: [
          {
            type: 'host',
            value: 'www.useadmesh.com',
          },
        ],
        destination: 'https://useadmesh.com/:path*',
        permanent: true,
      },
      // Redirect HTTP to HTTPS
      {
        source: '/:path*',
        has: [
          {
            type: 'header',
            key: 'x-forwarded-proto',
            value: 'http',
          },
        ],
        destination: 'https://useadmesh.com/:path*',
        permanent: true,
      },
    ];
  },
};

export default nextConfig;
