import { revalidateTag, revalidatePath } from 'next/cache'
import { NextRequest, NextResponse } from 'next/server'

/**
 * Manual revalidation endpoint for emergency cache clearing
 * Usage: POST /api/revalidate-all?secret=your_secret
 * 
 * This is useful when:
 * - Webhook is not working
 * - Need to force refresh all blog content
 * - Testing cache invalidation
 */
export async function POST(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const secret = searchParams.get('secret')

    // Check for revalidation secret
    if (!process.env.SANITY_REVALIDATE_SECRET) {
      return new Response('Missing environment variable SANITY_REVALIDATE_SECRET', { 
        status: 500 
      })
    }

    // Verify secret
    if (secret !== process.env.SANITY_REVALIDATE_SECRET) {
      return new Response('Invalid secret', { status: 401 })
    }

    console.log('Manual revalidation triggered')

    // Revalidate all blog-related tags
    revalidateTag('blog-posts')
    revalidateTag('blog-post')
    
    // Revalidate all blog paths
    revalidatePath('/blog')
    revalidatePath('/blog/sitemap.xml')
    revalidatePath('/blog/rss.xml')
    
    // Note: Individual blog post paths will be revalidated on next visit
    // due to the time-based revalidation (60 seconds)

    console.log('Manual revalidation completed')

    return NextResponse.json({ 
      message: 'Manual revalidation successful',
      timestamp: new Date().toISOString(),
      revalidated: [
        'blog-posts tag',
        'blog-post tag', 
        '/blog path',
        '/blog/sitemap.xml path',
        '/blog/rss.xml path'
      ]
    })

  } catch (err) {
    console.error('Manual revalidation error:', err)
    return new Response(
      JSON.stringify({ 
        message: 'Internal server error',
        error: err instanceof Error ? err.message : 'Unknown error'
      }), 
      { status: 500 }
    )
  }
}

// Also support GET for easier testing
export async function GET(req: NextRequest) {
  return new Response(
    JSON.stringify({
      message: 'Manual revalidation endpoint',
      usage: 'POST /api/revalidate-all?secret=your_secret',
      description: 'Revalidates all blog-related cache tags and paths'
    }),
    { 
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    }
  )
}
