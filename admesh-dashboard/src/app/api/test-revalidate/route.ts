import { revalidateTag, revalidatePath } from 'next/cache'
import { NextRequest, NextResponse } from 'next/server'

/**
 * Test revalidation endpoint (no signature validation)
 * Only for development testing
 */
export async function POST(req: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return new Response('Not available in production', { status: 403 })
    }

    const body = await req.json()
    
    console.log('Test revalidation triggered with payload:', body)

    // Revalidate blog-related tags
    revalidateTag('blog-posts')
    revalidateTag('blog-post')
    
    // If we have a specific slug, revalidate that specific post
    if (body.slug?.current) {
      revalidateTag(`blog-post-${body.slug.current}`)
      revalidatePath(`/blog/${body.slug.current}`)
      console.log(`Revalidated specific blog post: ${body.slug.current}`)
    }
    
    // Revalidate the main blog page
    revalidatePath('/blog')
    
    // Revalidate blog sitemap and RSS
    revalidatePath('/blog/sitemap.xml')
    revalidatePath('/blog/rss.xml')
    
    console.log('Test revalidation completed')

    return NextResponse.json({ 
      message: 'Test revalidation successful',
      type: body._type || 'unknown',
      slug: body.slug?.current || null,
      timestamp: new Date().toISOString()
    })

  } catch (err) {
    console.error('Test revalidation error:', err)
    return new Response(
      JSON.stringify({ 
        message: 'Internal server error',
        error: err instanceof Error ? err.message : 'Unknown error'
      }), 
      { status: 500 }
    )
  }
}

export async function GET() {
  if (process.env.NODE_ENV === 'production') {
    return new Response('Not available in production', { status: 403 })
  }

  return NextResponse.json({
    message: 'Test revalidation endpoint',
    usage: 'POST /api/test-revalidate with JSON payload',
    example: {
      _type: 'post',
      slug: { current: 'test-post' }
    }
  })
}
